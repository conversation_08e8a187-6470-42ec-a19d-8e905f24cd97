package com.endovas.cps.pojo.fo.patient;

import com.endovas.cps.entity.patient.PatientOrganPerfusionAssessment;
import io.daige.starter.common.javabean.BeanConvert;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

/**
 * 患者脏器灌注不良表现评估表单对象
 * Created by IntelliJ IDEA.
 *
 * @author: system
 * Date: 2025/07/29
 * Time: 16:00
 */
@Setter
@Getter
public class PatientOrganPerfusionAssessmentFO implements BeanConvert<PatientOrganPerfusionAssessmentFO,
        PatientOrganPerfusionAssessment> {

    @ApiModelProperty(value = "患者ID", required = true)
    @NotEmpty(message = "患者ID不能为空")
    private String patientId;

    // ========== 1. 中枢神经系统受累 (脑/脊髓) ==========
    @ApiModelProperty(value = "中枢神经系统临床表现：晕厥/意识障碍/脑血管意外/下肢轻瘫/截瘫等，多个值用逗号分隔")
    private String cnsSymptom;

    @ApiModelProperty(value = "其他神经系统症状描述")
    private String cnsSymptomOther;

    @ApiModelProperty(value = "影像学/神经学证据")
    private String cnsEvidence;

    @ApiModelProperty(value = "中枢神经系统受累备注")
    private String cnsNotes;

    // ========== 2. 肾脏受累 ==========
    @ApiModelProperty(value = "肾脏临床表现：血尿/少尿无尿/严重高血压等，多个值用逗号分隔")
    private String renalSymptom;

    @ApiModelProperty(value = "其他肾脏症状描述")
    private String renalSymptomOther;

    @ApiModelProperty(value = "肌酐值(umol/L)")
    private String creatinine;

    @ApiModelProperty(value = "尿素氮值(mmol/L)")
    private String ureaNitrogen;

    @ApiModelProperty(value = "肾脏影像学证据")
    private String renalImaging;

    @ApiModelProperty(value = "肾脏受累备注")
    private String renalNotes;

    // ========== 3. 胃肠道/腹腔脏器受累 ==========
    @ApiModelProperty(value = "胃肠道/腹腔脏器临床表现：急腹症/肠坏死/黑便血便/肝脏梗死/脾脏梗死等，多个值用逗号分隔")
    private String giSymptom;

    @ApiModelProperty(value = "其他胃肠道/腹腔症状描述")
    private String giSymptomOther;

    @ApiModelProperty(value = "胃肠道/腹腔脏器影像学证据")
    private String giImaging;

    @ApiModelProperty(value = "胃肠道/腹腔脏器受累备注")
    private String giNotes;

    // ========== 4. 下肢动脉受累 ==========
    @ApiModelProperty(value = "下肢动脉临床表现：疼痛/无脉/下肢缺血坏死等，多个值用逗号分隔")
    private String limbSymptom;

    @ApiModelProperty(value = "疼痛部位描述")
    private String limbPainLocation;

    @ApiModelProperty(value = "无脉部位描述")
    private String limbPulselessLocation;

    @ApiModelProperty(value = "其他下肢症状描述")
    private String limbSymptomOther;

    @ApiModelProperty(value = "血管超声/CTA证据")
    private String limbImaging;

    @ApiModelProperty(value = "下肢动脉受累备注")
    private String limbNotes;
}
