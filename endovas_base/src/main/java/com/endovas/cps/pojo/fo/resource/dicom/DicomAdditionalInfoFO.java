package com.endovas.cps.pojo.fo.resource.dicom;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2025/7/29
 * Time: 14:44
 */
@Getter
@Setter
public class DicomAdditionalInfoFO {
    @ApiModelProperty(value = "dicomId")
    @NotBlank(message = "dicomId不能为空")
    private String id;

    @ApiModelProperty(value = "临床症状")
    private String clinicalSymptoms;
    @ApiModelProperty(value = "临床诊断")
    private String clinicalDiagnosis;
    @ApiModelProperty(value = "检查所见")
    private String examinationFindings;

}
