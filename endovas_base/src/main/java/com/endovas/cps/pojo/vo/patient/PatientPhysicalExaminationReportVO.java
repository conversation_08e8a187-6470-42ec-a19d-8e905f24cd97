package com.endovas.cps.pojo.vo.patient;

import com.endovas.cps.entity.patient.PatientPhysicalExaminationReport;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 患者体征检查报告视图对象
 * Created by IntelliJ IDEA.
 *
 * @author: system
 * Date: 2025/07/29
 * Time: 15:00
 */
@Setter
@Getter
public class PatientPhysicalExaminationReportVO extends BaseVO implements BeanConvert<PatientPhysicalExaminationReportVO, PatientPhysicalExaminationReport> {

    @ApiModelProperty(value = "报告ID")
    private String id;

    @ApiModelProperty(value = "患者ID")
    private String patientId;

    // ========== 1. 血压异常 ==========
    @ApiModelProperty(value = "右上肢血压")
    private String bpRightUpper;

    @ApiModelProperty(value = "左上肢血压")
    private String bpLeftUpper;

    @ApiModelProperty(value = "右下肢血压")
    private String bpRightLower;

    @ApiModelProperty(value = "左下肢血压")
    private String bpLeftLower;

    @ApiModelProperty(value = "血压状态：高血压/正常/低血压/休克")
    private String bpStatus;

    @ApiModelProperty(value = "血压状态其他描述")
    private String bpStatusOther;

    @ApiModelProperty(value = "血压异常备注")
    private String bpNotes;

    // ========== 2. 心脏体征 ==========
    @ApiModelProperty(value = "主动脉瓣区体征：舒张期杂音/无明显杂音等，多个值用逗号分隔")
    private String cardiacPe;

    @ApiModelProperty(value = "杂音性质/级别描述")
    private String cardiacPeMurmurDesc;

    @ApiModelProperty(value = "心脏既往史：无心脏病史/有心脏病史")
    private String cardiacHx;

    @ApiModelProperty(value = "心脏既往史描述")
    private String cardiacHxDesc;

    @ApiModelProperty(value = "心脏体征备注")
    private String cardiacNotes;

    // ========== 3. 胸部体征 ==========
    @ApiModelProperty(value = "胸部视诊/触诊：气管偏移/无明显异常等，多个值用逗号分隔")
    private String chestPe;

    @ApiModelProperty(value = "气管偏移方向")
    private String tracheaShiftDirection;

    @ApiModelProperty(value = "胸部叩诊：浊音/鼓音/清音")
    private String chestPercussion;

    @ApiModelProperty(value = "胸部叩诊部位")
    private String chestPercussionLocation;

    @ApiModelProperty(value = "胸部听诊：呼吸音减弱/双肺湿罗音/呼吸音正常等，多个值用逗号分隔")
    private String chestAuscultation;

    @ApiModelProperty(value = "呼吸音减弱部位")
    private String respiratorySoundWeakLocation;

    @ApiModelProperty(value = "胸部体征备注")
    private String chestNotes;

    // ========== 4. 腹部体征 ==========
    @ApiModelProperty(value = "腹部视诊/触诊：腹部膨隆/广泛压痛/反跳痛/肌紧张/无明显异常等，多个值用逗号分隔")
    private String abdPe;

    @ApiModelProperty(value = "腹部体征其他描述")
    private String abdPeOther;

    @ApiModelProperty(value = "腹部叩诊：鼓音/浊音/清音")
    private String abdPercussion;

    @ApiModelProperty(value = "腹部叩诊描述")
    private String abdPercussionDesc;

    @ApiModelProperty(value = "腹部体征备注")
    private String abdNotes;

    // ========== 5. 神经系统体征 ==========
    @ApiModelProperty(value = "脑供血障碍：淡漠嗜睡/昏迷/偏瘫/意识清醒等，多个值用逗号分隔")
    private String neuroBrainPe;

    @ApiModelProperty(value = "脑供血障碍描述：偏瘫部位或其他描述")
    private String neuroBrainPeDesc;

    @ApiModelProperty(value = "脊髓供血障碍：下肢肌力减弱/截瘫/无明显异常等，多个值用逗号分隔")
    private String neuroSpinalPe;

    @ApiModelProperty(value = "脊髓供血障碍描述：肌力等级或其他描述")
    private String neuroSpinalPeDesc;

    @ApiModelProperty(value = "神经系统体征备注")
    private String neuroNotes;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
}
