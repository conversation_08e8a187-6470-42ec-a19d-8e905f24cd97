package com.endovas.cps.pojo.vo.patient;

import com.endovas.cps.entity.patient.PatientCardiacComplicationAssessment;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 患者心脏并发症表现评估视图对象
 * Created by IntelliJ IDEA.
 *
 * @author: system
 * Date: 2025/07/29
 * Time: 17:00
 */
@Setter
@Getter
public class PatientCardiacComplicationAssessmentVO extends BaseVO implements BeanConvert<PatientCardiacComplicationAssessmentVO, PatientCardiacComplicationAssessment> {

    @ApiModelProperty(value = "评估ID")
    private String id;

    @ApiModelProperty(value = "患者ID")
    private String patientId;

    // ========== 1. 主动脉瓣关闭不全 (AR) ==========
    @ApiModelProperty(value = "主动脉瓣关闭不全临床表现：心悸/气促/乏力/呼吸困难/心源性休克表现等，多个值用逗号分隔")
    private String arSymptom;

    @ApiModelProperty(value = "主动脉瓣关闭不全其他症状描述")
    private String arSymptomOther;

    @ApiModelProperty(value = "主动脉瓣关闭不全查体：主动脉瓣区舒张期杂音/脉压差增大/水冲脉等，多个值用逗号分隔")
    private String arPe;

    @ApiModelProperty(value = "主动脉瓣区舒张期杂音描述")
    private String arPeMurmurDesc;

    @ApiModelProperty(value = "主动脉瓣关闭不全其他查体描述")
    private String arPeOther;

    @ApiModelProperty(value = "AR程度：轻度/中度/重度")
    private String arDegree;

    @ApiModelProperty(value = "主动脉根部内径(cm)")
    private String arRootDiameter;

    @ApiModelProperty(value = "主动脉瓣对合：良好/不良")
    private String arValveCoaptation;

    @ApiModelProperty(value = "其他超声心动图发现")
    private String arEchoOther;

    @ApiModelProperty(value = "主动脉瓣关闭不全严重程度/备注")
    private String arSeverityNotes;

    // ========== 2. 急性心肌缺血/梗死 ==========
    @ApiModelProperty(value = "急性心肌缺血/梗死临床表现：胸痛/胸闷/气短/大汗等，多个值用逗号分隔")
    private String miSymptom;

    @ApiModelProperty(value = "胸痛性质描述")
    private String miChestPainNature;

    @ApiModelProperty(value = "胸痛部位描述")
    private String miChestPainLocation;

    @ApiModelProperty(value = "急性心肌缺血/梗死其他症状描述")
    private String miSymptomOther;

    @ApiModelProperty(value = "急性心肌缺血/梗死查体：无特殊阳性体征等，多个值用逗号分隔")
    private String miPe;

    @ApiModelProperty(value = "急性心肌缺血/梗死其他查体描述")
    private String miPeOther;

    @ApiModelProperty(value = "心电图ST段：抬高/压低/无变化")
    private String miEcgSt;

    @ApiModelProperty(value = "心电图T波：改变/无变化")
    private String miEcgT;

    @ApiModelProperty(value = "病理性Q波：有/无")
    private String miEcgQ;

    @ApiModelProperty(value = "cTnI值(ng/mL)")
    private String miCtni;

    @ApiModelProperty(value = "cTnI高峰值(ng/mL)")
    private String miCtniPeak;

    @ApiModelProperty(value = "CK-MB值(U/L)")
    private String miCkmb;

    @ApiModelProperty(value = "冠脉开口受累：有/无")
    private String miCoronaryInvolved;

    @ApiModelProperty(value = "冠脉开口受累描述")
    private String miCoronaryInvolvedDesc;

    @ApiModelProperty(value = "室壁运动异常：有/无")
    private String miWallMotionAbnormal;

    @ApiModelProperty(value = "室壁运动异常描述")
    private String miWallMotionAbnormalDesc;

    @ApiModelProperty(value = "急性心肌缺血/梗死严重程度/备注")
    private String miSeverityNotes;

    // ========== 3. 心包积液/心包压塞 ==========
    @ApiModelProperty(value = "心包积液/心包压塞临床表现：胸闷/气促/心悸/头晕/血压下降等，多个值用逗号分隔")
    private String pcSymptom;

    @ApiModelProperty(value = "心包积液/心包压塞其他症状描述")
    private String pcSymptomOther;

    @ApiModelProperty(value = "心包积液/心包压塞查体：心音遥远/颈静脉怒张/奇脉等，多个值用逗号分隔")
    private String pcPe;

    @ApiModelProperty(value = "心包积液/心包压塞其他查体描述")
    private String pcPeOther;

    @ApiModelProperty(value = "心包积液量(ml)")
    private String pcEffusionVolume;

    @ApiModelProperty(value = "心包积液性质：少量/中量/大量")
    private String pcEffusionNature;

    @ApiModelProperty(value = "心包压塞征象：有/无")
    private String pcTamponadeSigns;

    @ApiModelProperty(value = "心包压塞征象描述")
    private String pcTamponadeSignsDesc;

    @ApiModelProperty(value = "心包积液/心包压塞其他影像发现")
    private String pcImagingOther;

    @ApiModelProperty(value = "心包积液/心包压塞严重程度/备注")
    private String pcSeverityNotes;

    // ========== 4. 心力衰竭 ==========
    @ApiModelProperty(value = "心力衰竭临床表现：呼吸困难/端坐呼吸/下肢水肿/乏力/少尿等，多个值用逗号分隔")
    private String hfSymptom;

    @ApiModelProperty(value = "心力衰竭其他症状描述")
    private String hfSymptomOther;

    @ApiModelProperty(value = "心力衰竭查体：肺部啰音/肝颈静脉反流/肝脏肿大等，多个值用逗号分隔")
    private String hfPe;

    @ApiModelProperty(value = "肺部啰音描述")
    private String hfPeRalesDesc;

    @ApiModelProperty(value = "心力衰竭其他查体描述")
    private String hfPeOther;

    @ApiModelProperty(value = "BNP/NT-proBNP值(pg/mL)")
    private String hfBnp;

    @ApiModelProperty(value = "LVEF值(%)")
    private String hfLvef;

    @ApiModelProperty(value = "心力衰竭其他超声心动图发现")
    private String hfEchoOther;

    @ApiModelProperty(value = "心力衰竭严重程度/备注")
    private String hfSeverityNotes;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
}
