package com.endovas.cps.pojo.vo.resource;

import com.endovas.cps.entity.resource.Dicom;
import com.endovas.cps.pojo.vo.AttachmentVO;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * @author: wk
 * @Date: 2023/7/27
 * @Time: 19:49
 */
@Getter
@Setter
@ApiModel("影像文件列表对象")
public class DicomListVO extends BaseVO implements BeanConvert<DicomListVO, Dicom> {
    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "手术类型")
    private String surgeryType;
    @ApiModelProperty(value = "影像类型")
    private String modality;
    @ApiModelProperty(value = "影像文件名称")
    private String name;
    @ApiModelProperty(value = "影像日期")
    private String contentDate;
    @ApiModelProperty(value = "医院名称")
    private String hospitalName;
    @ApiModelProperty(value = "患者姓名")
    private String patientName;
    @ApiModelProperty(value = "状态")
    private String status;
    @ApiModelProperty(value = "是否包含dicom文件")
    private Boolean hasDicomFile;
    @ApiModelProperty(value = "上传人")
    private String creator;
    @ApiModelProperty(value = "上传人类型")
    private String creatorType;
    @ApiModelProperty(value = "上传人id")
    private String creatorId;

    private String patientId;
    private String gender;

    private String hospitalId;
    private String surgeryTypeId;

    @ApiModelProperty(value = "上传时间")
    private LocalDateTime createTime;

    private AttachmentVO dicom;
    private String viewJsonUrl;

    @ApiModelProperty(value = "Dicom预览图")
    private AttachmentVO dicomPic;
    @ApiModelProperty(value = "临床症状")
    private String clinicalSymptoms;
    @ApiModelProperty(value = "临床诊断")
    private String clinicalDiagnosis;
    @ApiModelProperty(value = "检查所见")
    private String examinationFindings;



}
