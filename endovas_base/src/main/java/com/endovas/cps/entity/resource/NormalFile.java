package com.endovas.cps.entity.resource;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/11/5
 * Time: 13:53
 */
@Setter
@Getter
@Accessors(chain = true)
@Entity
@Table(name = "t_normal_file")
@TableName("t_normal_file")
public class NormalFile extends MysqlBase {
    private String name;
    private String patientId; //关联patient
    //上传人归属
    private String creatorBelong;
    //字节
    private Long  fileSize;
    private String remark;


}
