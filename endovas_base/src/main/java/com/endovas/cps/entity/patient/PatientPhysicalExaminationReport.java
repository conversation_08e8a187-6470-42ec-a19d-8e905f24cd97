package com.endovas.cps.entity.patient;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 患者体征检查报告实体类
 * Created by IntelliJ IDEA.
 *
 * @author: system
 * Date: 2025/07/29
 * Time: 14:00
 */
@Getter
@Setter
@Entity
@Accessors(chain = true)
@Table(name = "t_patient_physical_examination_report")
@TableName("t_patient_physical_examination_report")
public class PatientPhysicalExaminationReport extends MysqlBase {
    
    public final static String PATIENT_ID = "patientId";
    public final static String BP_STATUS = "bpStatus";
    public final static String CARDIAC_HX = "cardiacHx";
    public final static String CHEST_PERCUSSION = "chestPercussion";
    public final static String ABD_PERCUSSION = "abdPercussion";
    
    // 关联患者ID
    private String patientId;
    
    // ========== 1. 血压异常 ==========
    @Column(columnDefinition = "varchar(50) COMMENT '右上肢血压'")
    private String bpRightUpper;
    
    @Column(columnDefinition = "varchar(50) COMMENT '左上肢血压'")
    private String bpLeftUpper;
    
    @Column(columnDefinition = "varchar(50) COMMENT '右下肢血压'")
    private String bpRightLower;
    
    @Column(columnDefinition = "varchar(50) COMMENT '左下肢血压'")
    private String bpLeftLower;
    
    @Column(columnDefinition = "varchar(50) COMMENT '血压状态：高血压/正常/低血压/休克'")
    private String bpStatus;
    
    @Column(columnDefinition = "varchar(500) COMMENT '血压状态其他描述'")
    private String bpStatusOther;
    
    @Column(columnDefinition = "text COMMENT '血压异常备注'")
    private String bpNotes;
    
    // ========== 2. 心脏体征 ==========
    @Column(columnDefinition = "varchar(200) COMMENT '主动脉瓣区体征：舒张期杂音/无明显杂音等，多个值用逗号分隔'")
    private String cardiacPe;
    
    @Column(columnDefinition = "varchar(200) COMMENT '杂音性质/级别描述'")
    private String cardiacPeMurmurDesc;
    
    @Column(columnDefinition = "varchar(50) COMMENT '心脏既往史：无心脏病史/有心脏病史'")
    private String cardiacHx;
    
    @Column(columnDefinition = "varchar(500) COMMENT '心脏既往史描述'")
    private String cardiacHxDesc;
    
    @Column(columnDefinition = "text COMMENT '心脏体征备注'")
    private String cardiacNotes;
    
    // ========== 3. 胸部体征 ==========
    @Column(columnDefinition = "varchar(200) COMMENT '胸部视诊/触诊：气管偏移/无明显异常等，多个值用逗号分隔'")
    private String chestPe;
    
    @Column(columnDefinition = "varchar(200) COMMENT '气管偏移方向'")
    private String tracheaShiftDirection;
    
    @Column(columnDefinition = "varchar(50) COMMENT '胸部叩诊：浊音/鼓音/清音'")
    private String chestPercussion;
    
    @Column(columnDefinition = "varchar(200) COMMENT '胸部叩诊部位'")
    private String chestPercussionLocation;
    
    @Column(columnDefinition = "varchar(200) COMMENT '胸部听诊：呼吸音减弱/双肺湿罗音/呼吸音正常等，多个值用逗号分隔'")
    private String chestAuscultation;
    
    @Column(columnDefinition = "varchar(200) COMMENT '呼吸音减弱部位'")
    private String respiratorySoundWeakLocation;
    
    @Column(columnDefinition = "text COMMENT '胸部体征备注'")
    private String chestNotes;
    
    // ========== 4. 腹部体征 ==========
    @Column(columnDefinition = "varchar(200) COMMENT '腹部视诊/触诊：腹部膨隆/广泛压痛/反跳痛/肌紧张/无明显异常等，多个值用逗号分隔'")
    private String abdPe;
    
    @Column(columnDefinition = "varchar(500) COMMENT '腹部体征其他描述'")
    private String abdPeOther;
    
    @Column(columnDefinition = "varchar(50) COMMENT '腹部叩诊：鼓音/浊音/清音'")
    private String abdPercussion;
    
    @Column(columnDefinition = "varchar(500) COMMENT '腹部叩诊描述'")
    private String abdPercussionDesc;
    
    @Column(columnDefinition = "text COMMENT '腹部体征备注'")
    private String abdNotes;
    
    // ========== 5. 神经系统体征 ==========
    @Column(columnDefinition = "varchar(200) COMMENT '脑供血障碍：淡漠嗜睡/昏迷/偏瘫/意识清醒等，多个值用逗号分隔'")
    private String neuroBrainPe;
    
    @Column(columnDefinition = "varchar(500) COMMENT '脑供血障碍描述：偏瘫部位或其他描述'")
    private String neuroBrainPeDesc;
    
    @Column(columnDefinition = "varchar(200) COMMENT '脊髓供血障碍：下肢肌力减弱/截瘫/无明显异常等，多个值用逗号分隔'")
    private String neuroSpinalPe;
    
    @Column(columnDefinition = "varchar(500) COMMENT '脊髓供血障碍描述：肌力等级或其他描述'")
    private String neuroSpinalPeDesc;
    
    @Column(columnDefinition = "text COMMENT '神经系统体征备注'")
    private String neuroNotes;
}
