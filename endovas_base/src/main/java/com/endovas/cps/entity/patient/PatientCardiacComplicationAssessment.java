package com.endovas.cps.entity.patient;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 患者心脏并发症表现评估实体类
 * Created by IntelliJ IDEA.
 *
 * @author: system
 * Date: 2025/07/29
 * Time: 17:00
 */
@Getter
@Setter
@Entity
@Accessors(chain = true)
@Table(name = "t_patient_cardiac_complication_assessment")
@TableName("t_patient_cardiac_complication_assessment")
public class PatientCardiacComplicationAssessment extends MysqlBase {
    
    public final static String PATIENT_ID = "patientId";
    public final static String AR_SYMPTOM = "arSymptom";
    public final static String MI_SYMPTOM = "miSymptom";
    public final static String PC_SYMPTOM = "pcSymptom";
    public final static String HF_SYMPTOM = "hfSymptom";
    
    // 关联患者ID
    private String patientId;
    
    // ========== 1. 主动脉瓣关闭不全 (AR) ==========
    @Column(columnDefinition = "varchar(300) COMMENT '主动脉瓣关闭不全临床表现：心悸/气促/乏力/呼吸困难/心源性休克表现等，多个值用逗号分隔'")
    private String arSymptom;
    
    @Column(columnDefinition = "varchar(500) COMMENT '主动脉瓣关闭不全其他症状描述'")
    private String arSymptomOther;
    
    @Column(columnDefinition = "varchar(300) COMMENT '主动脉瓣关闭不全查体：主动脉瓣区舒张期杂音/脉压差增大/水冲脉等，多个值用逗号分隔'")
    private String arPe;
    
    @Column(columnDefinition = "varchar(500) COMMENT '主动脉瓣区舒张期杂音描述'")
    private String arPeMurmurDesc;
    
    @Column(columnDefinition = "varchar(500) COMMENT '主动脉瓣关闭不全其他查体描述'")
    private String arPeOther;
    
    @Column(columnDefinition = "varchar(50) COMMENT 'AR程度：轻度/中度/重度'")
    private String arDegree;
    
    @Column(columnDefinition = "varchar(50) COMMENT '主动脉根部内径(cm)'")
    private String arRootDiameter;
    
    @Column(columnDefinition = "varchar(50) COMMENT '主动脉瓣对合：良好/不良'")
    private String arValveCoaptation;
    
    @Column(columnDefinition = "text COMMENT '其他超声心动图发现'")
    private String arEchoOther;
    
    @Column(columnDefinition = "text COMMENT '主动脉瓣关闭不全严重程度/备注'")
    private String arSeverityNotes;
    
    // ========== 2. 急性心肌缺血/梗死 ==========
    @Column(columnDefinition = "varchar(300) COMMENT '急性心肌缺血/梗死临床表现：胸痛/胸闷/气短/大汗等，多个值用逗号分隔'")
    private String miSymptom;
    
    @Column(columnDefinition = "varchar(200) COMMENT '胸痛性质描述'")
    private String miChestPainNature;
    
    @Column(columnDefinition = "varchar(200) COMMENT '胸痛部位描述'")
    private String miChestPainLocation;
    
    @Column(columnDefinition = "varchar(500) COMMENT '急性心肌缺血/梗死其他症状描述'")
    private String miSymptomOther;
    
    @Column(columnDefinition = "varchar(300) COMMENT '急性心肌缺血/梗死查体：无特殊阳性体征等，多个值用逗号分隔'")
    private String miPe;
    
    @Column(columnDefinition = "varchar(500) COMMENT '急性心肌缺血/梗死其他查体描述'")
    private String miPeOther;
    
    @Column(columnDefinition = "varchar(50) COMMENT '心电图ST段：抬高/压低/无变化'")
    private String miEcgSt;
    
    @Column(columnDefinition = "varchar(50) COMMENT '心电图T波：改变/无变化'")
    private String miEcgT;
    
    @Column(columnDefinition = "varchar(50) COMMENT '病理性Q波：有/无'")
    private String miEcgQ;
    
    @Column(columnDefinition = "varchar(50) COMMENT 'cTnI值(ng/mL)'")
    private String miCtni;
    
    @Column(columnDefinition = "varchar(50) COMMENT 'cTnI高峰值(ng/mL)'")
    private String miCtniPeak;
    
    @Column(columnDefinition = "varchar(50) COMMENT 'CK-MB值(U/L)'")
    private String miCkmb;
    
    @Column(columnDefinition = "varchar(50) COMMENT '冠脉开口受累：有/无'")
    private String miCoronaryInvolved;
    
    @Column(columnDefinition = "varchar(500) COMMENT '冠脉开口受累描述'")
    private String miCoronaryInvolvedDesc;
    
    @Column(columnDefinition = "varchar(50) COMMENT '室壁运动异常：有/无'")
    private String miWallMotionAbnormal;
    
    @Column(columnDefinition = "varchar(500) COMMENT '室壁运动异常描述'")
    private String miWallMotionAbnormalDesc;
    
    @Column(columnDefinition = "text COMMENT '急性心肌缺血/梗死严重程度/备注'")
    private String miSeverityNotes;

    // ========== 3. 心包积液/心包压塞 ==========
    @Column(columnDefinition = "varchar(300) COMMENT '心包积液/心包压塞临床表现：胸闷/气促/心悸/头晕/血压下降等，多个值用逗号分隔'")
    private String pcSymptom;

    @Column(columnDefinition = "varchar(500) COMMENT '心包积液/心包压塞其他症状描述'")
    private String pcSymptomOther;

    @Column(columnDefinition = "varchar(300) COMMENT '心包积液/心包压塞查体：心音遥远/颈静脉怒张/奇脉等，多个值用逗号分隔'")
    private String pcPe;

    @Column(columnDefinition = "varchar(500) COMMENT '心包积液/心包压塞其他查体描述'")
    private String pcPeOther;

    @Column(columnDefinition = "varchar(50) COMMENT '心包积液量(ml)'")
    private String pcEffusionVolume;

    @Column(columnDefinition = "varchar(50) COMMENT '心包积液性质：少量/中量/大量'")
    private String pcEffusionNature;

    @Column(columnDefinition = "varchar(50) COMMENT '心包压塞征象：有/无'")
    private String pcTamponadeSigns;

    @Column(columnDefinition = "varchar(500) COMMENT '心包压塞征象描述'")
    private String pcTamponadeSignsDesc;

    @Column(columnDefinition = "text COMMENT '心包积液/心包压塞其他影像发现'")
    private String pcImagingOther;

    @Column(columnDefinition = "text COMMENT '心包积液/心包压塞严重程度/备注'")
    private String pcSeverityNotes;

    // ========== 4. 心力衰竭 ==========
    @Column(columnDefinition = "varchar(300) COMMENT '心力衰竭临床表现：呼吸困难/端坐呼吸/下肢水肿/乏力/少尿等，多个值用逗号分隔'")
    private String hfSymptom;

    @Column(columnDefinition = "varchar(500) COMMENT '心力衰竭其他症状描述'")
    private String hfSymptomOther;

    @Column(columnDefinition = "varchar(300) COMMENT '心力衰竭查体：肺部啰音/肝颈静脉反流/肝脏肿大等，多个值用逗号分隔'")
    private String hfPe;

    @Column(columnDefinition = "varchar(500) COMMENT '肺部啰音描述'")
    private String hfPeRalesDesc;

    @Column(columnDefinition = "varchar(500) COMMENT '心力衰竭其他查体描述'")
    private String hfPeOther;

    @Column(columnDefinition = "varchar(50) COMMENT 'BNP/NT-proBNP值(pg/mL)'")
    private String hfBnp;

    @Column(columnDefinition = "varchar(50) COMMENT 'LVEF值(%)'")
    private String hfLvef;

    @Column(columnDefinition = "text COMMENT '心力衰竭其他超声心动图发现'")
    private String hfEchoOther;

    @Column(columnDefinition = "text COMMENT '心力衰竭严重程度/备注'")
    private String hfSeverityNotes;
}
