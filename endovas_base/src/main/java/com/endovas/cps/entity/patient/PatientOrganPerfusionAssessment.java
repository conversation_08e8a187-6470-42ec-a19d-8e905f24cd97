package com.endovas.cps.entity.patient;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 患者脏器灌注不良表现评估实体类
 * Created by IntelliJ IDEA.
 *
 * @author: system
 * Date: 2025/07/29
 * Time: 16:00
 */
@Getter
@Setter
@Entity
@Accessors(chain = true)
@Table(name = "t_patient_organ_perfusion_assessment")
@TableName("t_patient_organ_perfusion_assessment")
public class PatientOrganPerfusionAssessment extends MysqlBase {
    
    public final static String PATIENT_ID = "patientId";
    public final static String CNS_SYMPTOM = "cnsSymptom";
    public final static String RENAL_SYMPTOM = "renalSymptom";
    public final static String GI_SYMPTOM = "giSymptom";
    public final static String LIMB_SYMPTOM = "limbSymptom";
    
    // 关联患者ID
    private String patientId;
    
    // ========== 1. 中枢神经系统受累 (脑/脊髓) ==========
    @Column(columnDefinition = "varchar(300) COMMENT '中枢神经系统临床表现：晕厥/意识障碍/脑血管意外/下肢轻瘫/截瘫等，多个值用逗号分隔'")
    private String cnsSymptom;
    
    @Column(columnDefinition = "varchar(500) COMMENT '其他神经系统症状描述'")
    private String cnsSymptomOther;
    
    @Column(columnDefinition = "text COMMENT '影像学/神经学证据'")
    private String cnsEvidence;
    
    @Column(columnDefinition = "text COMMENT '中枢神经系统受累备注'")
    private String cnsNotes;
    
    // ========== 2. 肾脏受累 ==========
    @Column(columnDefinition = "varchar(200) COMMENT '肾脏临床表现：血尿/少尿无尿/严重高血压等，多个值用逗号分隔'")
    private String renalSymptom;
    
    @Column(columnDefinition = "varchar(500) COMMENT '其他肾脏症状描述'")
    private String renalSymptomOther;
    
    @Column(columnDefinition = "varchar(50) COMMENT '肌酐值(umol/L)'")
    private String creatinine;
    
    @Column(columnDefinition = "varchar(50) COMMENT '尿素氮值(mmol/L)'")
    private String ureaNitrogen;
    
    @Column(columnDefinition = "text COMMENT '肾脏影像学证据'")
    private String renalImaging;
    
    @Column(columnDefinition = "text COMMENT '肾脏受累备注'")
    private String renalNotes;
    
    // ========== 3. 胃肠道/腹腔脏器受累 ==========
    @Column(columnDefinition = "varchar(300) COMMENT '胃肠道/腹腔脏器临床表现：急腹症/肠坏死/黑便血便/肝脏梗死/脾脏梗死等，多个值用逗号分隔'")
    private String giSymptom;
    
    @Column(columnDefinition = "varchar(500) COMMENT '其他胃肠道/腹腔症状描述'")
    private String giSymptomOther;
    
    @Column(columnDefinition = "text COMMENT '胃肠道/腹腔脏器影像学证据'")
    private String giImaging;
    
    @Column(columnDefinition = "text COMMENT '胃肠道/腹腔脏器受累备注'")
    private String giNotes;
    
    // ========== 4. 下肢动脉受累 ==========
    @Column(columnDefinition = "varchar(200) COMMENT '下肢动脉临床表现：疼痛/无脉/下肢缺血坏死等，多个值用逗号分隔'")
    private String limbSymptom;
    
    @Column(columnDefinition = "varchar(200) COMMENT '疼痛部位描述'")
    private String limbPainLocation;
    
    @Column(columnDefinition = "varchar(200) COMMENT '无脉部位描述'")
    private String limbPulselessLocation;
    
    @Column(columnDefinition = "varchar(500) COMMENT '其他下肢症状描述'")
    private String limbSymptomOther;
    
    @Column(columnDefinition = "text COMMENT '血管超声/CTA证据'")
    private String limbImaging;
    
    @Column(columnDefinition = "text COMMENT '下肢动脉受累备注'")
    private String limbNotes;
}
