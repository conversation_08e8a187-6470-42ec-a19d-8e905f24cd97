package com.endovas.cps.service.patient.impl;

import com.endovas.cps.dao.patient.PatientDAO;
import com.endovas.cps.dao.patient.PatientPhysicalExaminationReportDAO;
import com.endovas.cps.entity.patient.PatientPhysicalExaminationReport;
import com.endovas.cps.pojo.fo.patient.PatientPhysicalExaminationReportFO;
import com.endovas.cps.pojo.vo.patient.PatientPhysicalExaminationReportVO;
import com.endovas.cps.service.patient.PatientPhysicalExaminationReportService;
import io.daige.starter.common.exception.BusinessAssertException;
import io.daige.starter.common.security.LoginUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * 患者体征检查报告服务实现类
 * Created by IntelliJ IDEA.
 *
 * @author: system
 * Date: 2025/07/29
 * Time: 15:00
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PatientPhysicalExaminationReportServiceImpl implements PatientPhysicalExaminationReportService {

    private final PatientPhysicalExaminationReportDAO patientPhysicalExaminationReportDAO;
    private final PatientDAO patientDAO;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String submit(PatientPhysicalExaminationReportFO input, LoginUser loginUser) {
        // 验证患者是否存在
        patientDAO.findById(input.getPatientId())
                .orElseThrow(() -> new BusinessAssertException("患者不存在"));

        // 查找是否已存在该患者的体征检查报告
        PatientPhysicalExaminationReport existingReport =
                patientPhysicalExaminationReportDAO.getByPatientId(input.getPatientId());

        PatientPhysicalExaminationReport report;
        if (Objects.nonNull(existingReport)) {
            // 如果已存在，则更新
            report = existingReport;
            input.convertTo(report);
        } else {
            // 如果不存在，则新建
            report = new PatientPhysicalExaminationReport();
            input.convertTo(report);
        }

        patientPhysicalExaminationReportDAO.save(report);
        return report.getId();
    }

    @Override
    public PatientPhysicalExaminationReportVO getByPatientId(String patientId, LoginUser loginUser) {
        // 验证患者是否存在
        patientDAO.findById(patientId)
                .orElseThrow(() -> new BusinessAssertException("患者不存在"));

        // 查找体征检查报告
        PatientPhysicalExaminationReport reportOpt =
                patientPhysicalExaminationReportDAO.getByPatientId(patientId);

        if (Objects.nonNull(reportOpt)) {
            PatientPhysicalExaminationReportVO result = new PatientPhysicalExaminationReportVO();
            result.convertFrom(reportOpt);
            return result;
        } else {
            // 如果没有报告，返回一个空的VO对象，只设置patientId
            PatientPhysicalExaminationReportVO result = new PatientPhysicalExaminationReportVO();
            result.setPatientId(patientId);
            return result;
        }
    }
}
