package com.endovas.cps.service.patient;

import com.endovas.cps.pojo.fo.patient.PatientOrganPerfusionAssessmentFO;
import com.endovas.cps.pojo.vo.patient.PatientOrganPerfusionAssessmentVO;
import io.daige.starter.common.security.LoginUser;

/**
 * 患者脏器灌注不良表现评估服务接口
 * Created by IntelliJ IDEA.
 *
 * @author: system
 * Date: 2025/07/29
 * Time: 16:00
 */
public interface PatientOrganPerfusionAssessmentService {
    
    /**
     * 提交脏器灌注不良表现评估
     * @param input 脏器灌注不良表现评估表单对象
     * @param loginUser 登录用户
     * @return 评估ID
     */
    String submit(PatientOrganPerfusionAssessmentFO input, LoginUser loginUser);
    
    /**
     * 根据患者ID查看脏器灌注不良表现评估
     * @param patientId 患者ID
     * @param loginUser 登录用户
     * @return 脏器灌注不良表现评估视图对象
     */
    PatientOrganPerfusionAssessmentVO getByPatientId(String patientId, LoginUser loginUser);
}
