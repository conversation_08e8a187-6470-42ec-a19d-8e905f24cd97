package com.endovas.cps.service.patient.impl;

import com.endovas.cps.dao.patient.PatientDAO;
import com.endovas.cps.dao.patient.PatientOrganPerfusionAssessmentDAO;
import com.endovas.cps.entity.patient.PatientOrganPerfusionAssessment;
import com.endovas.cps.pojo.fo.patient.PatientOrganPerfusionAssessmentFO;
import com.endovas.cps.pojo.vo.patient.PatientOrganPerfusionAssessmentVO;
import com.endovas.cps.service.patient.PatientOrganPerfusionAssessmentService;
import io.daige.starter.common.exception.BusinessAssertException;
import io.daige.starter.common.security.LoginUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * 患者脏器灌注不良表现评估服务实现类
 * Created by IntelliJ IDEA.
 *
 * @author: system
 * Date: 2025/07/29
 * Time: 16:00
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PatientOrganPerfusionAssessmentServiceImpl implements PatientOrganPerfusionAssessmentService {

    private final PatientOrganPerfusionAssessmentDAO patientOrganPerfusionAssessmentDAO;
    private final PatientDAO patientDAO;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String submit(PatientOrganPerfusionAssessmentFO input, LoginUser loginUser) {
        // 验证患者是否存在
        patientDAO.findById(input.getPatientId())
                .orElseThrow(() -> new BusinessAssertException("患者不存在"));

        // 查找是否已存在该患者的脏器灌注不良表现评估
        PatientOrganPerfusionAssessment existingAssessment =
                patientOrganPerfusionAssessmentDAO.getByPatientId(input.getPatientId());

        PatientOrganPerfusionAssessment assessment;
        if (Objects.nonNull(existingAssessment)) {
            // 如果已存在，则更新
            assessment = existingAssessment;
            input.convertTo(assessment);
        } else {
            // 如果不存在，则新建
            assessment = new PatientOrganPerfusionAssessment();
            input.convertTo(assessment);
        }

        patientOrganPerfusionAssessmentDAO.save(assessment);
        return assessment.getId();
    }

    @Override
    public PatientOrganPerfusionAssessmentVO getByPatientId(String patientId, LoginUser loginUser) {
        // 验证患者是否存在
        patientDAO.findById(patientId)
                .orElseThrow(() -> new BusinessAssertException("患者不存在"));

        // 查找脏器灌注不良表现评估
        PatientOrganPerfusionAssessment assessmentOpt =
                patientOrganPerfusionAssessmentDAO.getByPatientId(patientId);

        if (Objects.nonNull(assessmentOpt)) {
            PatientOrganPerfusionAssessmentVO result = new PatientOrganPerfusionAssessmentVO();
            result.convertFrom(assessmentOpt);
            return result;
        } else {
            // 如果没有评估，返回一个空的VO对象，只设置patientId
            PatientOrganPerfusionAssessmentVO result = new PatientOrganPerfusionAssessmentVO();
            result.setPatientId(patientId);
            return result;
        }
    }
}
