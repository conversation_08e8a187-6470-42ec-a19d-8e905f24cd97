package com.endovas.cps.service.resource;

import com.endovas.cps.enums.SurgeryStageEnum;
import com.endovas.cps.pojo.fo.resource.dicom.DicomAddFO;
import com.endovas.cps.pojo.fo.resource.dicom.DicomAdditionalInfoFO;
import com.endovas.cps.pojo.fo.resource.dicom.DicomEditFO;
import com.endovas.cps.pojo.fo.resource.dicom.DicomSearchFO;
import com.endovas.cps.pojo.vo.FileUploadKeyParam;
import com.endovas.cps.pojo.vo.resource.DicomListVO;
import com.endovas.cps.pojo.vo.resource.DicomSelectVO;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.security.LoginUser;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/7
 * Time: 14:27
 */
public interface DicomService {
    PageVO<DicomListVO> list(DicomSearchFO param,SurgeryStageEnum surgeryStageEnum, PageFO pageFO, LoginUser loginUser);

    List<DicomListVO> list(String patientId,SurgeryStageEnum surgeryStageEnum,LoginUser loginUser);


    List<DicomListVO> refreshStatus(List<String> ids,LoginUser loginUser);

    void edit(DicomEditFO param);
    void additionalInfo(DicomAdditionalInfoFO input);

    void del(String id);

    void updateStatusToFail(DicomSearchFO param, LoginUser loginUser);

    void updateStatusToProcess(String id);

    void updateTempFileStatusToFinish(String id);

    FileUploadKeyParam save(DicomAddFO input, SurgeryStageEnum stageEnum, LoginUser loginUser);

    List<DicomSelectVO> select(Boolean excludePatientId, SurgeryStageEnum surgeryStageEnum,LoginUser loginUser);
}
