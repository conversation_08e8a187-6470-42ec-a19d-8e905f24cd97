package com.endovas.cps.service.patient.impl;

import com.endovas.cps.dao.patient.PatientDAO;
import com.endovas.cps.dao.patient.PatientCardiacComplicationAssessmentDAO;
import com.endovas.cps.entity.patient.PatientCardiacComplicationAssessment;
import com.endovas.cps.pojo.fo.patient.PatientCardiacComplicationAssessmentFO;
import com.endovas.cps.pojo.vo.patient.PatientCardiacComplicationAssessmentVO;
import com.endovas.cps.service.patient.PatientCardiacComplicationAssessmentService;
import io.daige.starter.common.exception.BusinessAssertException;
import io.daige.starter.common.security.LoginUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * 患者心脏并发症表现评估服务实现类
 * Created by IntelliJ IDEA.
 *
 * @author: system
 * Date: 2025/07/29
 * Time: 17:00
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PatientCardiacComplicationAssessmentServiceImpl implements PatientCardiacComplicationAssessmentService {

    private final PatientCardiacComplicationAssessmentDAO patientCardiacComplicationAssessmentDAO;
    private final PatientDAO patientDAO;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String submit(PatientCardiacComplicationAssessmentFO input, LoginUser loginUser) {
        patientDAO.findById(input.getPatientId())
                      .orElseThrow(() -> new BusinessAssertException("患者不存在"));

        // 查找是否已存在该患者的心脏并发症表现评估
        PatientCardiacComplicationAssessment existingAssessment = 
                patientCardiacComplicationAssessmentDAO.getByPatientId(input.getPatientId());

        PatientCardiacComplicationAssessment assessment;
        if (Objects.nonNull(existingAssessment)) {
            // 如果已存在，则更新
            assessment = existingAssessment;
            input.convertTo(assessment);
        } else {
            // 如果不存在，则新建
            assessment = new PatientCardiacComplicationAssessment();
            input.convertTo(assessment);
        }

        patientCardiacComplicationAssessmentDAO.save(assessment);
        return assessment.getId();
    }

    @Override
    public PatientCardiacComplicationAssessmentVO getByPatientId(String patientId, LoginUser loginUser) {
        patientDAO.findById(patientId)
                 .orElseThrow(() -> new BusinessAssertException("患者不存在"));

        // 查找心脏并发症表现评估
        PatientCardiacComplicationAssessment assessment = 
                patientCardiacComplicationAssessmentDAO.getByPatientId(patientId);

        if (Objects.nonNull(assessment)) {
            PatientCardiacComplicationAssessmentVO result = new PatientCardiacComplicationAssessmentVO();
            result.convertFrom(assessment);
            return result;
        } else {
            // 如果没有评估，返回一个空的VO对象，只设置patientId
            PatientCardiacComplicationAssessmentVO result = new PatientCardiacComplicationAssessmentVO();
            result.setPatientId(patientId);
            return result;
        }
    }
}
