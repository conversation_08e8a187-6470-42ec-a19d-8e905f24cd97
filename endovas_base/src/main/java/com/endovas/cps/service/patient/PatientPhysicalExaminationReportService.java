package com.endovas.cps.service.patient;

import com.endovas.cps.pojo.fo.patient.PatientPhysicalExaminationReportFO;
import com.endovas.cps.pojo.vo.patient.PatientPhysicalExaminationReportVO;
import io.daige.starter.common.security.LoginUser;

/**
 * 患者体征检查报告服务接口
 * Created by IntelliJ IDEA.
 *
 * @author: system
 * Date: 2025/07/29
 * Time: 15:00
 */
public interface PatientPhysicalExaminationReportService {
    
    /**
     * 提交体征检查报告
     * @param input 体征检查报告表单对象
     * @param loginUser 登录用户
     * @return 报告ID
     */
    String submit(PatientPhysicalExaminationReportFO input, LoginUser loginUser);
    
    /**
     * 根据患者ID查看体征检查报告
     * @param patientId 患者ID
     * @param loginUser 登录用户
     * @return 体征检查报告视图对象
     */
    PatientPhysicalExaminationReportVO getByPatientId(String patientId, LoginUser loginUser);
}
