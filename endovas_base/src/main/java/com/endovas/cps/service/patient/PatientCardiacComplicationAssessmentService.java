package com.endovas.cps.service.patient;

import com.endovas.cps.pojo.fo.patient.PatientCardiacComplicationAssessmentFO;
import com.endovas.cps.pojo.vo.patient.PatientCardiacComplicationAssessmentVO;
import io.daige.starter.common.security.LoginUser;

/**
 * 患者心脏并发症表现评估服务接口
 * Created by IntelliJ IDEA.
 *
 * @author: system
 * Date: 2025/07/29
 * Time: 17:00
 */
public interface PatientCardiacComplicationAssessmentService {
    
    /**
     * 提交心脏并发症表现评估
     * @param input 心脏并发症表现评估表单对象
     * @param loginUser 登录用户
     * @return 评估ID
     */
    String submit(PatientCardiacComplicationAssessmentFO input, LoginUser loginUser);
    
    /**
     * 根据患者ID查看心脏并发症表现评估
     * @param patientId 患者ID
     * @param loginUser 登录用户
     * @return 心脏并发症表现评估视图对象
     */
    PatientCardiacComplicationAssessmentVO getByPatientId(String patientId, LoginUser loginUser);
}
