package com.endovas.cps.dao.patient;

import com.endovas.cps.entity.patient.PatientCardiacComplicationAssessment;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import io.daige.starter.component.jpa.annotation.SoftDelete;
import org.springframework.stereotype.Repository;

/**
 * 患者心脏并发症表现评估DAO
 * Created by IntelliJ IDEA.
 *
 * @author: system
 * Date: 2025/07/29
 * Time: 17:00
 */
@SoftDelete
@Repository
public interface PatientCardiacComplicationAssessmentDAO extends MysqlBaseRepo<PatientCardiacComplicationAssessment> {
    
    /**
     * 根据患者ID查找心脏并发症表现评估
     * @param patientId 患者ID
     * @return 心脏并发症表现评估
     */
    PatientCardiacComplicationAssessment getByPatientId(String patientId);
}
