package com.endovas.cps.dao.patient;

import com.endovas.cps.entity.patient.PatientPhysicalExaminationReport;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import io.daige.starter.component.jpa.annotation.SoftDelete;
import org.springframework.stereotype.Repository;

/**
 * 患者体征检查报告DAO
 * Created by IntelliJ IDEA.
 *
 * @author: system
 * Date: 2025/07/29
 * Time: 15:00
 */
@SoftDelete
@Repository
public interface PatientPhysicalExaminationReportDAO extends MysqlBaseRepo<PatientPhysicalExaminationReport> {
    PatientPhysicalExaminationReport getByPatientId(String patientId);
    
}
