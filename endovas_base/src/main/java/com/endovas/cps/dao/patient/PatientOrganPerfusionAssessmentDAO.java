package com.endovas.cps.dao.patient;

import com.endovas.cps.entity.patient.PatientOrganPerfusionAssessment;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import io.daige.starter.component.jpa.annotation.SoftDelete;
import org.springframework.stereotype.Repository;

/**
 * 患者脏器灌注不良表现评估DAO
 * Created by IntelliJ IDEA.
 *
 * @author: system
 * Date: 2025/07/29
 * Time: 16:00
 */
@SoftDelete
@Repository
public interface PatientOrganPerfusionAssessmentDAO extends MysqlBaseRepo<PatientOrganPerfusionAssessment> {
    
    /**
     * 根据患者ID查找脏器灌注不良表现评估
     * @param patientId 患者ID
     * @return 脏器灌注不良表现评估
     */
    PatientOrganPerfusionAssessment getByPatientId(String patientId);

}
