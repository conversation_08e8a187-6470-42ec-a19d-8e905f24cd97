package com.endovas.cps.controller.web.platform.patient;

import com.endovas.cps.pojo.fo.patient.PatientCardiacComplicationAssessmentFO;
import com.endovas.cps.pojo.vo.patient.PatientCardiacComplicationAssessmentVO;
import com.endovas.cps.service.patient.PatientCardiacComplicationAssessmentService;
import io.daige.starter.common.Project;
import io.daige.starter.common.render.BaseResult;
import io.daige.starter.common.render.Render;
import io.daige.starter.common.render.RenderJson;
import io.daige.starter.common.security.CurrentUser;
import io.daige.starter.common.security.LoginUser;
import io.daige.starter.common.utils.BizAssert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 患者心脏并发症表现评估控制器
 * Created by IntelliJ IDEA.
 *
 * @author: system
 * Date: 2025/07/29
 * Time: 17:00
 */
@Slf4j
@RestController
@Api(value = "患者心脏并发症表现评估", tags = "患者心脏并发症表现评估")
@RequestMapping(Project.PLATFORM + "/patient/cardiacComplicationAssessment")
@RequiredArgsConstructor
public class PatientCardiacComplicationAssessmentController {

    private final PatientCardiacComplicationAssessmentService patientCardiacComplicationAssessmentService;

    @ApiOperation(value = "提交心脏并发症表现评估", notes = "提交或更新患者的心脏并发症表现评估")
    @PostMapping(path = "/submit")
    public String submit(@Validated PatientCardiacComplicationAssessmentFO input,
                         @ApiIgnore @CurrentUser LoginUser loginUser) {
        String assessmentId = patientCardiacComplicationAssessmentService.submit(input, loginUser);
        Map<String, String> result = new HashMap<>();
        result.put("assessmentId", assessmentId);
        return RenderJson.success(result);
    }

    @ApiOperation(value = "查看心脏并发症表现评估", notes = "根据患者ID查看心脏并发症表现评估")
    @GetMapping(path = "/getByPatientId")
    public BaseResult<PatientCardiacComplicationAssessmentVO> getByPatientId(
            @RequestParam String patientId,
            @ApiIgnore @CurrentUser LoginUser loginUser) {
        BizAssert.notEmpty(patientId, "患者ID不能为空");
        PatientCardiacComplicationAssessmentVO result =
                patientCardiacComplicationAssessmentService.getByPatientId(patientId, loginUser);
        return Render.success(result);
    }
}
