package com.endovas.cps.controller.web.platform.patient;

import com.endovas.cps.pojo.fo.patient.PatientOrganPerfusionAssessmentFO;
import com.endovas.cps.pojo.vo.patient.PatientOrganPerfusionAssessmentVO;
import com.endovas.cps.service.patient.PatientOrganPerfusionAssessmentService;
import io.daige.starter.common.Project;
import io.daige.starter.common.render.BaseResult;
import io.daige.starter.common.render.Render;
import io.daige.starter.common.render.RenderJson;
import io.daige.starter.common.security.CurrentUser;
import io.daige.starter.common.security.LoginUser;
import io.daige.starter.common.utils.BizAssert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 患者脏器灌注不良表现评估控制器
 * Created by IntelliJ IDEA.
 *
 * @author: system
 * Date: 2025/07/29
 * Time: 16:00
 */
@Slf4j
@RestController
@Api(value = "患者脏器灌注不良表现评估", tags = "患者脏器灌注不良表现评估")
@RequestMapping(Project.PLATFORM + "/patient/organPerfusionAssessment")
@RequiredArgsConstructor
public class PatientOrganPerfusionAssessmentController {

    private final PatientOrganPerfusionAssessmentService patientOrganPerfusionAssessmentService;

    @ApiOperation(value = "提交脏器灌注不良表现评估", notes = "提交或更新患者的脏器灌注不良表现评估")
    @PostMapping(path = "/submit")
    public String submit(@Validated  PatientOrganPerfusionAssessmentFO input,
                        @ApiIgnore @CurrentUser LoginUser loginUser) {
        String assessmentId = patientOrganPerfusionAssessmentService.submit(input, loginUser);
        Map<String, String> result = new HashMap<>();
        result.put("assessmentId", assessmentId);
        return RenderJson.success(result);
    }

    @ApiOperation(value = "查看脏器灌注不良表现评估", notes = "根据患者ID查看脏器灌注不良表现评估")
    @GetMapping(path = "/getByPatientId")
    public BaseResult<PatientOrganPerfusionAssessmentVO> getByPatientId(
            @RequestParam String patientId,
            @ApiIgnore @CurrentUser LoginUser loginUser) {
        BizAssert.notEmpty(patientId, "患者ID不能为空");
        PatientOrganPerfusionAssessmentVO result = 
                patientOrganPerfusionAssessmentService.getByPatientId(patientId, loginUser);
        return Render.success(result);
    }
}
