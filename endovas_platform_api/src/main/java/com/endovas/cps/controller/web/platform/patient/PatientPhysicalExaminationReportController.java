package com.endovas.cps.controller.web.platform.patient;

import com.endovas.cps.pojo.fo.patient.PatientPhysicalExaminationReportFO;
import com.endovas.cps.pojo.vo.patient.PatientPhysicalExaminationReportVO;
import com.endovas.cps.service.patient.PatientPhysicalExaminationReportService;
import io.daige.starter.common.Project;
import io.daige.starter.common.render.BaseResult;
import io.daige.starter.common.render.Render;
import io.daige.starter.common.render.RenderJson;
import io.daige.starter.common.security.CurrentUser;
import io.daige.starter.common.security.LoginUser;
import io.daige.starter.common.utils.BizAssert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 患者体征检查报告控制器
 * Created by IntelliJ IDEA.
 *
 * @author: system
 * Date: 2025/07/29
 * Time: 15:00
 */
@Slf4j
@RestController
@Api(value = "患者体征检查报告", tags = "患者体征检查报告")
@RequestMapping(Project.PLATFORM + "/patient/physicalExaminationReport")
@RequiredArgsConstructor
public class PatientPhysicalExaminationReportController {

    private final PatientPhysicalExaminationReportService patientPhysicalExaminationReportService;

    @ApiOperation(value = "提交体征检查报告", notes = "提交或更新患者的体征检查报告")
    @PostMapping(path = "/submit")
    public String submit(@Validated  PatientPhysicalExaminationReportFO input,
                        @ApiIgnore @CurrentUser LoginUser loginUser) {
        String reportId = patientPhysicalExaminationReportService.submit(input, loginUser);
        Map<String, String> result = new HashMap<>();
        result.put("reportId", reportId);
        return RenderJson.success(result);
    }

    @ApiOperation(value = "查看体征检查报告", notes = "根据患者ID查看体征检查报告")
    @GetMapping(path = "/getByPatientId")
    public BaseResult<PatientPhysicalExaminationReportVO> getByPatientId(
            @RequestParam String patientId,
            @ApiIgnore @CurrentUser LoginUser loginUser) {
        BizAssert.notEmpty(patientId, "患者ID不能为空");
        PatientPhysicalExaminationReportVO result = 
                patientPhysicalExaminationReportService.getByPatientId(patientId, loginUser);
        return Render.success(result);
    }
}
